"""
LangGraph State 定义 (V3.0 - 统一架构版)

定义基于统一架构的标准化Agent状态对象，支持两阶段工作流和事件驱动架构。
"""
from typing import TypedDict, List, Dict, Any, Optional, Literal, Annotated
from typing_extensions import TypedDict
try:
    from langgraph.graph.message import add_messages
except ImportError:
    # 如果没有安装langgraph，提供一个简单的替代实现
    def add_messages(existing: List[Dict], new: List[Dict]) -> List[Dict]:
        return existing + new


class StandardAgentState(TypedDict):
    """标准化Agent状态对象 (V3.0)

    这是整个LangGraph工作流中流动的单一事实来源(Single Source of Truth)。
    与UnifiedEventBus和UnifiedToolRegistry深度集成，支持两阶段工作流：
    1. 意图分析阶段：framework_analysis + preference_analysis
    2. ICP迭代规划阶段：思考-行动-观察循环
    """

    # ==================== 基础消息流 ====================
    # 基础消息流，支持LangGraph的消息传递机制
    messages: Annotated[List[Dict[str, Any]], add_messages]

    # ==================== 任务标识与控制 ====================
    # 任务唯一标识符
    task_id: str
    # 用户ID
    user_id: str
    # 用户原始查询
    original_query: str
    # 当前执行阶段：initialization/framework_analysis/preference_analysis/planning/completed
    current_phase: str
    # 执行模式：automatic(全自动)/interactive(交互式)
    execution_mode: Literal["automatic", "interactive"]

    # ==================== 意图分析结果（两步整合模式）====================
    # 核心框架分析结果：目的地、天数、主题、预算等核心要素
    framework_analysis: Optional[Dict[str, Any]]
    # 个性化偏好分析结果：景点、美食、住宿偏好
    preference_analysis: Optional[Dict[str, Any]]
    # 整合后的统一意图上下文，供ICP规划使用
    consolidated_intent: Optional[Dict[str, Any]]

    # ==================== ICP迭代规划状态 ====================
    # 规划思考日志，记录AI的每一步推理过程
    planning_log: List[str]
    # 当前待执行的动作指令 {"tool_name": "search_poi", "parameters": {...}}
    current_action: Optional[Dict[str, Any]]
    # 按天存储的POI规划结果 {1: [...], 2: [...]}
    daily_plans: Dict[int, List[Dict[str, Any]]]
    # 按天的时间跟踪器，记录已用时间（分钟）
    daily_time_tracker: Dict[int, int]
    # 总预算跟踪器
    total_budget_tracker: float

    # ==================== 工具执行结果 ====================
    # 工具执行结果缓存 {"search_poi": [...], "get_weather": {...}}
    tool_results: Dict[str, Any]

    # ==================== 事件驱动状态 ====================
    # 通知服务实例，用于发布事件到UnifiedEventBus
    notification_service: Optional[Any]

    # ==================== 用户画像与上下文 ====================
    # 用户画像信息
    user_profile: Optional[Dict[str, Any]]
    # 用户历史记忆
    user_memories: Optional[List[Dict[str, Any]]]
    # 车辆信息（自驾场景）
    vehicle_info: Optional[Dict[str, Any]]

    # ==================== 最终结果 ====================
    # 最终生成的完整行程
    final_itinerary: Optional[Dict[str, Any]]
    # 任务是否完成
    is_completed: bool
    # 是否有错误
    has_error: bool
    # 错误信息
    error_message: Optional[str]

    # ==================== 兼容性字段 ====================
    # 为了与现有代码兼容，保留一些关键字段
    # 这些字段会在后续版本中逐步移除
    destinations: Optional[List[str]]  # 从framework_analysis中提取
    core_intent: Optional[Dict[str, Any]]  # 映射到framework_analysis
    multi_city_strategy: Optional[Dict[str, Any]]  # 从framework_analysis中提取
    driving_context: Optional[Dict[str, Any]]  # 从framework_analysis中提取


# ==================== 向后兼容别名 ====================
# 为了保持向后兼容性，提供旧状态类的别名
TravelPlanState = StandardAgentState
