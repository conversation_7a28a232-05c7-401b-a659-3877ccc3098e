"""
TravelPlannerAgent LangGraph工作流图 (V3.0 - 统一架构版)

基于统一架构的两阶段工作流：
1. 意图分析阶段：framework_analysis + preference_analysis
2. ICP迭代规划阶段：思考-行动-观察循环

集成UnifiedEventBus、UnifiedToolRegistry和StandardAgentState
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver

from .state import StandardAgentState
from .nodes import (
    run_framework_analysis,
    run_preference_analysis,
    prepare_planning_context,
    run_icp_planning
)
from src.services.unified_event_bus import UnifiedEventBus
from src.tools.unified_registry import unified_registry

logger = logging.getLogger(__name__)


class TravelPlannerGraphV3:
    """
    旅行规划Agent图形类 (V3.0 - 统一架构版)
    
    实现基于统一架构的两阶段工作流：
    1. 意图分析：快速确定旅行框架和个性化偏好
    2. ICP规划：AI驱动的迭代式上下文规划
    """
    
    def __init__(self, event_bus: Optional[UnifiedEventBus] = None):
        """
        初始化图形
        
        Args:
            event_bus: 统一事件总线实例
        """
        self.event_bus = event_bus
        self.graph = None
        self.checkpointer = MemorySaver()
        
        # 连接事件总线到工具注册表
        if self.event_bus:
            unified_registry.set_event_bus(self.event_bus)
        
        logger.info("TravelPlannerGraphV3 initialized")
    
    def _create_graph(self) -> StateGraph:
        """创建LangGraph工作流图"""
        
        # 创建状态图
        graph_builder = StateGraph(StandardAgentState)
        
        # 添加节点
        graph_builder.add_node("framework_analysis", run_framework_analysis)
        graph_builder.add_node("preference_analysis", run_preference_analysis)
        graph_builder.add_node("prepare_context", prepare_planning_context)
        graph_builder.add_node("icp_planning", run_icp_planning)
        
        # 定义工作流边
        graph_builder.add_edge(START, "framework_analysis")
        graph_builder.add_edge("framework_analysis", "preference_analysis")
        graph_builder.add_edge("preference_analysis", "prepare_context")
        graph_builder.add_edge("prepare_context", "icp_planning")
        graph_builder.add_edge("icp_planning", END)
        
        # 编译图
        return graph_builder.compile(checkpointer=self.checkpointer)
    
    def compile(self) -> StateGraph:
        """编译图形"""
        if self.graph is None:
            self.graph = self._create_graph()
            logger.info("TravelPlannerGraphV3 compiled successfully")
        return self.graph
    
    async def invoke(
        self, 
        input_data: Dict[str, Any], 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        执行旅行规划工作流
        
        Args:
            input_data: 输入数据
            config: 配置参数
            
        Returns:
            规划结果
        """
        if self.graph is None:
            self.compile()
        
        # 准备初始状态
        initial_state = self._prepare_initial_state(input_data)
        
        # 初始化任务状态
        if self.event_bus:
            await self.event_bus.initialize_task(
                initial_state["task_id"], 
                initial_state
            )
        
        try:
            # 执行工作流
            result = await self.graph.ainvoke(initial_state, config)
            
            # 发布最终结果
            if self.event_bus and result.get("is_completed", False):
                await self.event_bus.notify_final_result(
                    initial_state["task_id"],
                    result.get("final_itinerary", {})
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Graph execution failed: {str(e)}")
            if self.event_bus:
                await self.event_bus.notify_error(
                    initial_state["task_id"],
                    str(e),
                    "graph_execution"
                )
            raise
    
    async def stream(
        self,
        input_data: Dict[str, Any],
        config: Optional[Dict[str, Any]] = None
    ):
        """
        流式执行旅行规划工作流
        
        Args:
            input_data: 输入数据
            config: 配置参数
            
        Yields:
            工作流执行步骤
        """
        if self.graph is None:
            self.compile()
        
        # 准备初始状态
        initial_state = self._prepare_initial_state(input_data)
        
        # 初始化任务状态
        if self.event_bus:
            await self.event_bus.initialize_task(
                initial_state["task_id"],
                initial_state
            )
        
        try:
            # 流式执行工作流
            async for step in self.graph.astream(initial_state, config):
                yield step
                
                # 同步状态到事件总线
                if self.event_bus:
                    for node_name, node_result in step.items():
                        if isinstance(node_result, dict):
                            await self.event_bus.sync_from_agent_state(
                                initial_state["task_id"],
                                node_result
                            )
            
        except Exception as e:
            logger.error(f"Graph streaming failed: {str(e)}")
            if self.event_bus:
                await self.event_bus.notify_error(
                    initial_state["task_id"],
                    str(e),
                    "graph_streaming"
                )
            raise
    
    def _prepare_initial_state(self, input_data: Dict[str, Any]) -> StandardAgentState:
        """
        准备初始状态
        
        Args:
            input_data: 输入数据
            
        Returns:
            初始化的StandardAgentState
        """
        # 生成任务ID
        task_id = input_data.get("task_id", f"task_{int(datetime.now().timestamp())}")
        
        # 构建初始消息
        user_query = input_data.get("user_query", "")
        messages = [{"role": "user", "content": user_query}]
        
        # 创建初始状态
        initial_state: StandardAgentState = {
            "messages": messages,
            "task_id": task_id,
            "user_id": input_data.get("user_id", "anonymous"),
            "original_query": user_query,
            "current_phase": "initialization",
            "execution_mode": input_data.get("execution_mode", "automatic"),
            "framework_analysis": None,
            "preference_analysis": None,
            "consolidated_intent": None,
            "planning_log": [],
            "current_action": None,
            "daily_plans": {},
            "daily_time_tracker": {},
            "total_budget_tracker": 0.0,
            "tool_results": {},
            "notification_service": self.event_bus,
            "user_profile": input_data.get("user_profile", {}),
            "user_memories": input_data.get("user_memories", []),
            "vehicle_info": input_data.get("vehicle_info"),
            "final_itinerary": None,
            "is_completed": False,
            "has_error": False,
            "error_message": None,
            # 兼容性字段
            "destinations": None,
            "core_intent": None,
            "multi_city_strategy": None,
            "driving_context": None
        }
        
        return initial_state
    
    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具注册信息"""
        return unified_registry.get_tool_info()


# 创建全局实例
def create_travel_planner_graph_v3(event_bus: Optional[UnifiedEventBus] = None) -> TravelPlannerGraphV3:
    """
    创建旅行规划图实例
    
    Args:
        event_bus: 事件总线实例
        
    Returns:
        TravelPlannerGraphV3实例
    """
    return TravelPlannerGraphV3(event_bus=event_bus)
