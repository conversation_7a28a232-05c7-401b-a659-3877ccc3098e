["tests/test_unified_architecture.py::TestStandardAgentState::test_state_structure", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_initialize_task", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_notify_error", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_notify_phase_end", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_notify_phase_start", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_sync_from_agent_state", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_execute_action_tool", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_execute_action_tool_with_event_bus", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_get_tool_info", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_register_action_tool", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_register_planner_tool"]